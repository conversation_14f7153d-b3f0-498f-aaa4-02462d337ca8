import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import UserSearchModal from '../user-search-modal'

// Mock fetch
const mockFetch = jest.fn()
global.fetch = mockFetch

const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    image: null,
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    image: null,
  },
]

describe('UserSearchModal', () => {
  const mockOnClose = jest.fn()
  const mockOnSelectUser = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ users: mockUsers }),
    })
  })

  it('does not render when closed', () => {
    render(
      <UserSearchModal
        isOpen={false}
        onClose={mockOnClose}
        onSelectUser={mockOnSelectUser}
      />
    )
    
    expect(screen.queryByText('Start New Conversation')).not.toBeInTheDocument()
  })

  it('renders when open', () => {
    render(
      <UserSearchModal
        isOpen={true}
        onClose={mockOnClose}
        onSelectUser={mockOnSelectUser}
      />
    )
    
    expect(screen.getByText('Start New Conversation')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Search users by name or email...')).toBeInTheDocument()
  })

  it('calls onClose when close button is clicked', () => {
    render(
      <UserSearchModal
        isOpen={true}
        onClose={mockOnClose}
        onSelectUser={mockOnSelectUser}
      />
    )
    
    // Click the close button (the only button in the header)
    const buttons = screen.getAllByRole('button')
    fireEvent.click(buttons[0]) // The close button is the first button
    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('searches for users when typing', async () => {
    const user = userEvent.setup()
    
    render(
      <UserSearchModal
        isOpen={true}
        onClose={mockOnClose}
        onSelectUser={mockOnSelectUser}
      />
    )
    
    const searchInput = screen.getByPlaceholderText('Search users by name or email...')
    await user.type(searchInput, 'john')
    
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/users/search?q=john')
    })
  })

  it('displays search results', async () => {
    const user = userEvent.setup()
    
    render(
      <UserSearchModal
        isOpen={true}
        onClose={mockOnClose}
        onSelectUser={mockOnSelectUser}
      />
    )
    
    const searchInput = screen.getByPlaceholderText('Search users by name or email...')
    await user.type(searchInput, 'john')
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    })
  })

  it('calls onSelectUser when user is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <UserSearchModal
        isOpen={true}
        onClose={mockOnClose}
        onSelectUser={mockOnSelectUser}
      />
    )
    
    const searchInput = screen.getByPlaceholderText('Search users by name or email...')
    await user.type(searchInput, 'john')
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
    })
    
    fireEvent.click(screen.getByText('John Doe'))
    
    expect(mockOnSelectUser).toHaveBeenCalledWith(mockUsers[0])
    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('shows no results message when no users found', async () => {
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ users: [] }),
    })
    
    const user = userEvent.setup()
    
    render(
      <UserSearchModal
        isOpen={true}
        onClose={mockOnClose}
        onSelectUser={mockOnSelectUser}
      />
    )
    
    const searchInput = screen.getByPlaceholderText('Search users by name or email...')
    await user.type(searchInput, 'nonexistent')
    
    await waitFor(() => {
      expect(screen.getByText('No users found')).toBeInTheDocument()
    })
  })

  it('shows minimum character message for short queries', () => {
    render(
      <UserSearchModal
        isOpen={true}
        onClose={mockOnClose}
        onSelectUser={mockOnSelectUser}
      />
    )
    
    expect(screen.getByText('Type at least 2 characters to search')).toBeInTheDocument()
  })
})
